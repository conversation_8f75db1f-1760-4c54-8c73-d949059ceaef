# AstroConnect Production Database Setup Guide

This guide provides step-by-step instructions to set up a local PostgreSQL database on your Ubuntu VPS server and migrate from the remote Prisma database.

## 🎯 Overview

We're switching from remote Prisma database to local PostgreSQL for:
- Better performance and control
- No external dependencies
- Cost savings
- Data sovereignty

## 📋 Prerequisites

- Ubuntu VPS server with sudo access
- Node.js and npm installed
- Your AstroConnect application deployed

## 🚀 Step 1: Install PostgreSQL on Ubuntu VPS

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install PostgreSQL and additional tools
sudo apt install postgresql postgresql-contrib postgresql-client -y

# Start and enable PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Check PostgreSQL status
sudo systemctl status postgresql
```

## 🔐 Step 2: Configure PostgreSQL

```bash
# Switch to postgres user
sudo -u postgres psql

# Inside PostgreSQL prompt, create database and user
CREATE DATABASE astroconnect_production;
CREATE USER astroconnect_user WITH PASSWORD 'your_secure_password_here';

# Grant privileges
GRANT ALL PRIVILEGES ON DATABASE astroconnect_production TO astroconnect_user;
ALTER USER astroconnect_user CREATEDB;

# Enable UUID extension
\c astroconnect_production;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

# Exit PostgreSQL
\q
```

## 🔧 Step 3: Configure PostgreSQL for Remote Connections (Optional)

If you need to connect from other servers:

```bash
# Edit PostgreSQL configuration
sudo nano /etc/postgresql/*/main/postgresql.conf

# Find and modify:
listen_addresses = 'localhost'  # Keep as localhost for security

# Edit pg_hba.conf for authentication
sudo nano /etc/postgresql/*/main/pg_hba.conf

# Add line for local connections:
local   all             astroconnect_user                               md5

# Restart PostgreSQL
sudo systemctl restart postgresql
```

## 📁 Step 4: Update Environment Variables

Update your production `.env.production` file:

```bash
# Navigate to your application directory
cd /path/to/your/astroconnect

# Edit environment file
nano .env.production
```

Update the DATABASE_URL:
```env
# Replace with your actual password
DATABASE_URL="postgresql://astroconnect_user:your_secure_password_here@localhost:5432/astroconnect_production"
```

## 🗄️ Step 5: Run Database Migrations

```bash
# Navigate to your application directory
cd /path/to/your/astroconnect

# Install dependencies if not already done
npm install

# Generate Prisma client
npx prisma generate

# Run all migrations to set up the schema
npx prisma migrate deploy

# Verify the database schema
npx prisma db pull
```

## 📊 Step 6: Data Migration (If Needed)

If you have existing data in the remote Prisma database that you want to migrate:

### Option A: Export/Import Data

```bash
# 1. Export data from remote database (run this locally first)
# Update .env temporarily to point to remote database
npx prisma db pull
npx prisma db seed  # If you have a seed script

# 2. Create a data export script (see data-export.js below)
node scripts/export-data.js

# 3. Upload the exported data to your server
scp exported-data.json user@your-server:/path/to/astroconnect/

# 4. Import data on server
node scripts/import-data.js
```

### Option B: Fresh Start (Recommended)

Since you mentioned the local database is working fine, you can start fresh:

```bash
# Create an admin user
npx prisma db seed  # If you have a seed script

# Or manually create admin via the application
# Visit: https://your-domain.com/admin/setup
```

## 🔒 Step 7: Security Configuration

```bash
# Create a backup user
sudo -u postgres createuser --no-createdb --no-createrole --no-superuser backup_user
sudo -u postgres psql -c "ALTER USER backup_user WITH PASSWORD 'backup_password';"
sudo -u postgres psql -c "GRANT CONNECT ON DATABASE astroconnect_production TO backup_user;"
sudo -u postgres psql -c "GRANT USAGE ON SCHEMA public TO backup_user;"
sudo -u postgres psql -c "GRANT SELECT ON ALL TABLES IN SCHEMA public TO backup_user;"

# Set up firewall (if not already configured)
sudo ufw allow 22/tcp  # SSH
sudo ufw allow 80/tcp  # HTTP
sudo ufw allow 443/tcp # HTTPS
# Do NOT open port 5432 unless absolutely necessary
sudo ufw enable
```

## 📦 Step 8: Backup Configuration

Create automated backups:

```bash
# Create backup directory
sudo mkdir -p /var/backups/astroconnect
sudo chown astroconnect_user:astroconnect_user /var/backups/astroconnect

# Create backup script
sudo nano /usr/local/bin/backup-astroconnect.sh
```

Add backup script content:
```bash
#!/bin/bash
BACKUP_DIR="/var/backups/astroconnect"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="astroconnect_production"
DB_USER="astroconnect_user"

# Create backup
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_DIR/astroconnect_$DATE.sql

# Keep only last 7 days of backups
find $BACKUP_DIR -name "astroconnect_*.sql" -mtime +7 -delete

echo "Backup completed: astroconnect_$DATE.sql"
```

```bash
# Make script executable
sudo chmod +x /usr/local/bin/backup-astroconnect.sh

# Add to crontab for daily backups at 2 AM
sudo crontab -e
# Add line: 0 2 * * * /usr/local/bin/backup-astroconnect.sh
```

## 🧪 Step 9: Test the Setup

```bash
# Test database connection
npx prisma db pull

# Test application
npm run build
npm start

# Check if application starts without errors
curl http://localhost:3000/api/health
```

## 🚨 Troubleshooting

### Common Issues:

1. **Connection refused**:
   ```bash
   sudo systemctl status postgresql
   sudo systemctl restart postgresql
   ```

2. **Permission denied**:
   ```bash
   sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE astroconnect_production TO astroconnect_user;"
   ```

3. **Migration errors**:
   ```bash
   npx prisma migrate reset --force
   npx prisma migrate deploy
   ```

4. **Port conflicts**:
   ```bash
   sudo netstat -tlnp | grep :5432
   ```

## ✅ Verification Checklist

- [ ] PostgreSQL installed and running
- [ ] Database and user created
- [ ] Environment variables updated
- [ ] Migrations applied successfully
- [ ] Application starts without errors
- [ ] Admin panel accessible
- [ ] Backup system configured
- [ ] Security measures in place

## 📞 Next Steps

1. Test all application features
2. Set up monitoring (optional)
3. Configure SSL certificates
4. Set up log rotation
5. Document your specific configuration

## 🔗 Useful Commands

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Connect to database
psql -U astroconnect_user -d astroconnect_production -h localhost

# View database size
psql -U astroconnect_user -d astroconnect_production -c "SELECT pg_size_pretty(pg_database_size('astroconnect_production'));"

# List all tables
psql -U astroconnect_user -d astroconnect_production -c "\dt"

# Check application logs
pm2 logs astroconnect  # If using PM2
journalctl -u your-app-service  # If using systemd
```

## 🚀 Quick Start Commands

For experienced users, here's the condensed version:

```bash
# Install PostgreSQL
sudo apt update && sudo apt install postgresql postgresql-contrib -y
sudo systemctl start postgresql && sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql -c "CREATE DATABASE astroconnect_production;"
sudo -u postgres psql -c "CREATE USER astroconnect_user WITH PASSWORD 'your_password';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE astroconnect_production TO astroconnect_user;"
sudo -u postgres psql -d astroconnect_production -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";"

# Update environment and deploy
cd /path/to/astroconnect
# Update DATABASE_URL in .env.production
npx prisma migrate deploy
npm run build
pm2 restart astroconnect  # or your process manager
```

## 📋 Deployment Checklist

- [ ] PostgreSQL installed and running
- [ ] Database and user created with proper permissions
- [ ] Environment variables updated in production
- [ ] All migrations applied successfully
- [ ] Application builds without errors
- [ ] Application starts and connects to database
- [ ] Admin panel accessible
- [ ] Birth chart functionality working (no schema errors)
- [ ] Backup system configured
- [ ] Security measures implemented
- [ ] SSL certificates configured
- [ ] Monitoring set up (optional)

---

**⚠️ Important Notes:**
- Replace `your_secure_password_here` with a strong password
- Update `/path/to/your/astroconnect` with your actual application path
- Test thoroughly before going live
- Keep backups of your current setup before making changes
- The schema mismatch issue has been resolved with the new migration
